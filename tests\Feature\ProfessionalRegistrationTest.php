<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Profile;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;

class ProfessionalRegistrationTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create storage disk for testing
        Storage::fake('website');
    }

    /** @test */
    public function it_can_upload_banner_image_in_step_5()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        // Create a profile for the user
        $profile = Profile::create([
            'user_id' => $user->id,
            'full_name' => 'Test User',
            'company_name' => 'Test Company',
        ]);

        // Create a fake image file
        $bannerImage = UploadedFile::fake()->image('banner.jpg', 860, 330);

        // Act as the authenticated user
        $response = $this->actingAs($user)
            ->post(route('register.professional.save_step'), [
                'step' => 5,
                'banner_image' => $bannerImage,
                '_token' => csrf_token(),
            ]);

        // Assert the response is successful
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);

        // Assert the file was stored
        $this->assertTrue(Storage::disk('website')->exists('banner-images/' . $bannerImage->hashName()));

        // Assert the profile was updated with the banner image path
        $profile->refresh();
        $this->assertNotNull($profile->banner_image);
        $this->assertStringContains('banner-images/', $profile->banner_image);
    }

    /** @test */
    public function it_validates_banner_image_file_type()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        // Create a profile for the user
        Profile::create([
            'user_id' => $user->id,
            'full_name' => 'Test User',
            'company_name' => 'Test Company',
        ]);

        // Create a fake non-image file
        $invalidFile = UploadedFile::fake()->create('document.pdf', 100);

        // Act as the authenticated user
        $response = $this->actingAs($user)
            ->post(route('register.professional.save_step'), [
                'step' => 5,
                'banner_image' => $invalidFile,
                '_token' => csrf_token(),
            ]);

        // Assert validation fails
        $response->assertStatus(200);
        $response->assertJson(['success' => false]);
    }

    /** @test */
    public function it_validates_banner_image_file_size()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        // Create a profile for the user
        Profile::create([
            'user_id' => $user->id,
            'full_name' => 'Test User',
            'company_name' => 'Test Company',
        ]);

        // Create a fake large image file (3MB)
        $largeImage = UploadedFile::fake()->image('large_banner.jpg')->size(3072);

        // Act as the authenticated user
        $response = $this->actingAs($user)
            ->post(route('register.professional.save_step'), [
                'step' => 5,
                'banner_image' => $largeImage,
                '_token' => csrf_token(),
            ]);

        // Assert validation fails
        $response->assertStatus(200);
        $response->assertJson(['success' => false]);
    }

    /** @test */
    public function it_can_save_step_5_without_banner_image()
    {
        // Create a test user
        $user = User::factory()->create([
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
        ]);

        // Create a profile for the user
        Profile::create([
            'user_id' => $user->id,
            'full_name' => 'Test User',
            'company_name' => 'Test Company',
        ]);

        // Act as the authenticated user without banner image
        $response = $this->actingAs($user)
            ->post(route('register.professional.save_step'), [
                'step' => 5,
                '_token' => csrf_token(),
            ]);

        // Assert the response is successful (banner image is optional)
        $response->assertStatus(200);
        $response->assertJson(['success' => true]);
    }
}
